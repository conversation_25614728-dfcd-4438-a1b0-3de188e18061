
import { useInView } from 'react-intersection-observer';
import { motion } from 'framer-motion';
import React from "react";

const TimelineFixed = () => {
  const { ref, inView } = useInView({ threshold: 0.2, triggerOnce: true });

  return (
    <section id="timeline" className="relative py-16 sm:py-20 lg:py-24 overflow-hidden font-['Open_Sans'] bg-white">
      {/* Removed Background Image and Overlay */}

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center">
          {/* Content Section */}
          <motion.div
            ref={ref}
            className={`transition-all duration-1000 w-full max-w-3xl text-left ${
              inView ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'
            }`}
            initial={{ opacity: 0, x: -100 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Enhanced Header */}
            <div className="mb-6 sm:mb-8">
              <motion.span
                className="inline-block px-4 py-2 bg-gray-100 border border-gray-300 rounded-full text-lg sm:text-xl font-bold text-gray-800 uppercase tracking-wider font-['Open_Sans'] mb-4"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                Our Story
              </motion.span>
              <motion.h2
                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 font-['Open_Sans'] leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={inView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: 0.4, duration: 0.8 }}
              >
                40+ Years of Power Excellence
              </motion.h2>
            </div>

            {/* Enhanced Content */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify">
                We offer solutions to industrial & commercial establishments under our popular brand KRYKARD. With over <span className="font-bold text-gray-900">5,00,000 installations</span> of Power Conditioners & over <span className="font-bold text-gray-700">1,50,000 installations</span> of Portable & Panel Load Managers, KRYKARD is one of the leading brands in Power Conditioning & Energy Management.
              </p>
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify">
                State-of-the-art facilities empower us to address the requirements of Indian industries comprehensively, effectively & efficiently, ensuring they derive maximum benefits from the power conditioning & energy management solutions we provide.
              </p>
              <p className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify">
                With a taskforce of around <span className="font-bold text-gray-900">500+ employees</span> & an extensive network of sales & service branches nationwide, we are well-equipped to seamlessly reach out to our customers & fulfil their needs.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default TimelineFixed;
