import React, { useState, useEffect, useRef } from 'react';
import { CheckCircle, Shield, Zap, Database, Eye, TrendingUp, Award, Monitor, BarChart3, Users, Target, Settings, ChevronDown, FileText, Brain, Clock, Search, Circle, ArrowRight, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, useAnimation, useInView } from 'framer-motion';
import PageLayout from '@/components/layout/PageLayout';

// Animated Particle Background Component
const AnimatedParticles = ({ count = 30, color = "green" }) => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-1 h-1 rounded-full bg-${color}-400/30`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -100, -200],
            opacity: [0, 0.8, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            delay: i * 0.1,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

// Floating Geometric Shapes Component
const FloatingShapes = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: 6 }).map((_, i) => (
        <motion.div
          key={`shape-${i}`}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            rotate: [0, 360],
            scale: [0.8, 1.2, 0.8],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            delay: i * 2,
            ease: "linear",
          }}
        >
          <Circle className="w-6 h-6 text-green-300/20" />
        </motion.div>
      ))}
    </div>
  );
};

// Mobile-Responsive Card Component
const MobileCard = ({ children, className = "" }) => (
  <motion.div
    className={`bg-white rounded-2xl shadow-lg border border-green-100 p-4 sm:p-6 lg:p-8 mb-4 ${className}`}
    whileHover={{ scale: 1.02 }}
    transition={{ duration: 0.2 }}
  >
    {children}
  </motion.div>
);

// Animated Counter Component
const AnimatedCounter = ({ value, label, icon: Icon, delay = 0 }) => {
  const [count, setCount] = useState(0);
  const [isInView, setIsInView] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isInView) {
          setIsInView(true);
          const target = typeof value === 'number' ? value : parseInt(value);
          const duration = 2000;
          const startTime = Date.now();

          const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(progress * target);
            setCount(current);

            if (progress < 1) {
              requestAnimationFrame(animate);
            }
          };

          setTimeout(() => requestAnimationFrame(animate), delay);
        }
      },
      { threshold: 0.5 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [value, isInView, delay]);

  return (
    <motion.div
      ref={ref}
      className="text-center p-4"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
    >
      <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-green-500 to-emerald-600 mb-3">
        <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
      </div>
      <div className="typography-h2 font-bold text-green-700 mb-2 font-['Open_Sans']">
        {typeof value === 'number' ? count : value}
      </div>
      <div className="typography-body text-black font-normal font-['Open_Sans']">{label}</div>
    </motion.div>
  );
};

const EnterpriseESGReporting = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModule, setSelectedModule] = useState(null);

  const stats = [
    { label: 'Data Accuracy', value: '99.9%', icon: TrendingUp },
    { label: 'Report Generation', value: '90% Faster', icon: Award },
    { label: 'Compliance Rate', value: 100, icon: Shield },
    { label: 'Data Trail Verification', value: 100, icon: Monitor }
  ];

  const keyBenefits = [
    {
      title: 'Seamless Data Collection',
      description: 'Ease of collection and aggregation of ESG data from across internal and external systems',
      icon: Database,
      features: ['ERP integration', 'Finance system sync', 'e-Procurement data', 'Supply chain tracking', 'Smart meters integration', 'IOT devices connectivity']
    },
    {
      title: 'AI-Powered Anomaly Detection',
      description: 'AI detects anomalies in data real-time, flagging potential errors before reports are finalized',
      icon: Brain,
      features: ['Real-time monitoring', 'Error detection', 'Automated flagging', 'Report validation', 'Data quality checks', 'Anomaly alerts']
    },
    {
      title: 'Pre-built Regulatory Templates',
      description: 'Pre-built templates aligned with regulatory standards like BRSR, GRI and CSRD',
      icon: FileText,
      features: ['BRSR templates', 'GRI standards', 'CSRD compliance', 'Regulatory updates', 'Standardized formats', 'Compliance tracking']
    },
    {
      title: 'Faster Reporting & Reduced Dependency',
      description: 'It cuts reporting time drastically and lowers consultant dependency',
      icon: Clock,
      features: ['Automated reports', 'Time reduction', 'Consultant independence', 'Quick generation', 'Self-service capabilities', 'Streamlined workflows']
    },
    {
      title: '100% Data Trail',
      description: 'Ensure 100% data trail of data making it verifiable and audit friendly',
      icon: Search,
      features: ['Full traceability', 'Audit-friendly', 'Data verification', 'Complete trail', 'Compliance documentation', 'Audit readiness']
    },
    {
      title: 'Real-time Insights & Dashboards',
      description: 'Enable real-time insights through Dashboards and alerts and facilitate quick identification of improvement opportunities',
      icon: TrendingUp,
      features: ['Live dashboards', 'Real-time alerts', 'Quick insights', 'Improvement tracking', 'Performance monitoring', 'Actionable analytics']
    }
  ];

  const coreFeatures = [
    'Centralized digital platform ensuring compliance & audit friendliness',
    'Eliminate inefficiencies in manual, people-driven, error-prone ESG Reporting',
    'Well-structured approach to replace manual processes',
    'Seamless data collection from multiple internal and external systems',
    'AI-powered anomaly detection and real-time validation',
    'Pre-built regulatory compliance templates (BRSR, GRI, CSRD)',
    'Automated reporting and documentation generation',
    'Complete audit trail and data verification',
    'Real-time insights through dashboards and alerts',
    'Drastic reduction in reporting time and consultant dependency'
  ];

  const businessBenefits = [
    {
      title: 'Digital Transformation',
      description: 'Transform from Manual to Digital ESG Compliance with a centralized platform',
      icon: Monitor
    },
    {
      title: 'Compliance Assurance',
      description: 'Ensure 100% compliance with regulatory standards and audit-friendly processes',
      icon: Shield
    },
    {
      title: 'Efficiency Gains',
      description: 'Eliminate inefficiencies and drastically reduce reporting time',
      icon: Zap
    },
    {
      title: 'Data Integrity',
      description: 'AI-powered validation ensures data accuracy and real-time error detection',
      icon: Database
    }
  ];

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(3deg); }
        }
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-12px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 5s ease-in-out infinite;
        }
        .animate-float-delayed {
          animation: float-delayed 6s ease-in-out infinite;
        }
        html {
          font-size: 16px;
        }
        body, .font-['Open_Sans'] {
          font-size: 1rem;
        }
        h1, h2, h3, h4, h5, h6 {
          line-height: 1.2;
        }
        @media (max-width: 640px) {
          html { font-size: 15px; }
          h1, h2, h3, h4, h5, h6 { font-size: 1.1em; }
        }
      `}</style>
      {/* Main Title Section with 3D/Blurred Background Elements */}
      <div className="relative bg-green-100 py-6 sm:py-8 lg:py-10 overflow-hidden">
        {/* 3D/Blurred Background Elements */}
        <div className="absolute inset-0 pointer-events-none z-0">
          <div className="absolute -top-10 -left-10 w-40 h-40 bg-green-300 opacity-30 rounded-full blur-3xl animate-float" />
          <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-emerald-200 opacity-20 rounded-full blur-2xl animate-float-delayed" />
          <div className="absolute -bottom-10 right-0 w-48 h-48 bg-green-200 opacity-25 rounded-full blur-3xl animate-float" />
          <div className="absolute top-10 right-1/4 w-24 h-24 bg-emerald-300 opacity-15 rounded-full blur-2xl animate-float-delayed" />
          <div className="absolute bottom-1/3 left-1/4 w-28 h-28 bg-green-400 opacity-10 rounded-full blur-2xl animate-float" />
        </div>
        {/* Main Content */}
        <div className="relative max-w-7xl mx-auto text-center px-4 sm:px-6 lg:px-8 z-20">
          <h1 className="typography-h1 font-bold text-green-900 mb-4 font-['Open_Sans']">
            Enterprise Esg Reporting
          </h1>
          <p className="typography-body text-black max-w-4xl mx-auto font-['Open_Sans']">
          Empowering organizations to seamlessly track, analyze, and report ESG (Environmental, Social, and Governance) performance with accuracy, transparency, and compliance.
          </p>
        </div>
      </div>
      <div className="font-['Open_Sans'] min-h-screen w-full overflow-x-hidden bg-[#e6f7f1] text-base sm:text-lg">
        {/* Sticky Header for Mobile */}
        <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-green-200 shadow-sm">
          <div className="px-4 py-3 sm:px-6 sm:py-4">
            <div className="text-center">
              {/* <h1 className="text-lg sm:text-xl font-bold text-green-700">ALENSOFT</h1>
              <h2 className="text-sm sm:text-base font-semibold text-gray-800">Enterprise ESG Reporting</h2> */}
            </div>
          </div>
        </div>

        {/* Hero Section with Mobile-First Design */}
        <motion.div
          className="relative overflow-hidden px-4 py-8 sm:px-6 sm:py-12 lg:px-8 lg:py-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          {/* Animated Background */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50"
            animate={{
              background: [
                "linear-gradient(135deg, rgb(240 253 244) 0%, rgb(236 253 245) 50%, rgb(240 253 250) 100%)",
                "linear-gradient(135deg, rgb(236 253 245) 0%, rgb(240 253 250) 50%, rgb(240 253 244) 100%)",
                "linear-gradient(135deg, rgb(240 253 250) 0%, rgb(240 253 244) 50%, rgb(236 253 245) 100%)",
              ]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          <FloatingShapes />
          <AnimatedParticles count={20} color="green" />

          <div className="relative max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              {/* Left Content */}
              <motion.div
                className="text-center lg:text-left space-y-6"
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                {/* Animated Badge */}
                <motion.div
                  className="inline-flex items-center px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm border border-green-200 shadow-lg"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <motion.div
                    className="w-2 h-2 bg-green-500 rounded-full mr-2"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [1, 0.5, 1]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <span className="typography-body text-black font-['Open_Sans']">Digital ESG Compliance</span>
                </motion.div>

                {/* Main Heading */}
                <div className="space-y-4">
                  <motion.h1
                    className="typography-h1 font-bold text-black font-['Open_Sans']"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                  >
                    <span className="block">ALENSOFT</span>
                    <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent typography-h2 font-['Open_Sans']">
                      Enterprise ESG Reporting
                    </span>
                  </motion.h1>
                </div>

                {/* Description */}
                <motion.p
                  className="typography-body text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0 font-['Open_Sans']"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                >
                  Helping industries to transition from Manual to Digital ESG Compliance with a comprehensive platform that ensures accuracy, compliance, and audit-friendliness.
                </motion.p>

                {/* Key Highlights */}
                <motion.div
                  className="flex flex-wrap gap-2 justify-center lg:justify-start"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.4 }}
                >
                  {['Digital Transformation', 'Audit Friendly', 'Compliance Assured'].map((highlight, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-full border border-green-200 shadow-sm"
                    >
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="typography-body text-black font-['Open_Sans']">{highlight}</span>
                    </div>
                  ))}
                </motion.div>
              </motion.div>

              {/* Right Content - Image */}
              <motion.div
                className="relative max-w-md mx-auto lg:max-w-lg"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                  <img
                    src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=600&h=400&fit=crop&auto=format"
                    alt="Enterprise ESG Reporting Platform"
                    className="w-full h-48 sm:h-56 lg:h-64 object-cover rounded-2xl"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Stats Section - Mobile Responsive */}
        <motion.div
          className="px-4 py-8 sm:px-6 sm:py-12 bg-white"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="typography-h1 font-bold text-black mb-6 font-['Open_Sans']">
                Platform Performance
              </h2>
              <p className="typography-h3 font-normal text-black max-w-2xl mx-auto font-['Open_Sans']">
                Proven results with ALENSOFT ESG Reporting Platform
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              {stats.map((stat, index) => (
                <AnimatedCounter
                  key={index}
                  value={stat.value}
                  label={stat.label}
                  icon={stat.icon}
                  delay={index * 0.2}
                />
              ))}
            </div>
          </div>
        </motion.div>

       {/* Challenges & Services Section */}
        <motion.div
          className="px-4 py-8 sm:px-6 sm:py-12 bg-gradient-to-r from-green-50 to-emerald-50"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <div className="max-w-6xl mx-auto">
            <MobileCard className="mb-8">
              <h2 className="typography-h1 font-bold text-black mb-8 font-['Open_Sans']">
                Key Challenges We Address
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
                {[
                  'Understanding the regulatory requirements',
                  'Conducting value chain assessment and determining the applicable scope & boundaries',
                  'Hand-holding for data collection and reporting'
                ].map((challenge, index) => (
                  <div key={index} className="flex items-start space-x-3 p-4 sm:p-5 bg-gradient-to-br from-green-100 to-emerald-50 rounded-xl">
                    <div className="bg-green-600 p-2 rounded-full mt-0.5 flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <span className="typography-body text-gray-800 font-medium font-['Open_Sans']">{challenge}</span>
                    </div>
                  </div>
                ))}
              </div>
            </MobileCard>

            <div className="space-y-6">
              <MobileCard>
                <h3 className="typography-h2 font-semibold text-black mb-6 flex items-start font-['Open_Sans']">
                  <div className="bg-green-600 p-2 rounded-lg mr-3 flex-shrink-0 mt-0.5">
                    <Settings className="w-4 h-4 text-white" />
                  </div>
                  <span>ESG Consulting</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[
                    'ESG diagnostics & baseline creation',
                    'Creation of ESG Strategy & Transformation Roadmap',
                    'Identification of sustainability related risks and opportunities & ESG Program Management',
                    'Emission reduction Targets / KPIs & Status Monitoring'
                  ].map((service, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="bg-green-500 p-1.5 rounded-full mt-1 flex-shrink-0">
                        <div className="w-1.5 h-1.5 bg-white rounded-full" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <span className="typography-body text-gray-800 font-['Open_Sans']">{service}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </MobileCard>

              <MobileCard>
                <h3 className="typography-h3 font-semibold text-black mb-4 font-['Open_Sans']">
                  <span className="bg-emerald-100 px-2 py-1 rounded-lg">AI powered integrated Digitization platform</span> for ESG Management & Reporting
                </h3>
                <p className="typography-body text-black font-normal font-['Open_Sans']">(BRSR, CSRD, GRI - Integrated Reports & Dashboards)</p>
              </MobileCard>
            </div>
          </div>
        </motion.div>

        {/* Tabbed Content Section */}
        <motion.div
          className="px-4 py-8 sm:px-6 sm:py-12 bg-white"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="typography-h1 font-bold text-black mb-6 font-['Open_Sans']">
                ALENSOFT ESG Platform
              </h2>
            </div>
 {/* Mobile-Friendly Tab Navigation */}
            <div className="flex flex-wrap justify-center mb-8 bg-white rounded-xl p-2 shadow-lg border border-green-200 gap-2">
              {[
                { id: 'overview', label: 'Platform Overview', icon: Target },
                { id: 'features', label: 'Core Features', icon: Settings },
                { id: 'benefits', label: 'Benefits', icon: Award }
              ].map((tab) => {
                const TabIcon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-4 py-3 rounded-lg font-semibold typography-body transition-all duration-300 font-['Open_Sans'] ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-lg'
                        : 'text-green-700 hover:bg-green-50'
                    }`}
                  >
                    <TabIcon className="w-4 h-4 mr-2" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </div>
           
           {/* Tab Content */}
            <div className="bg-white rounded-xl shadow-lg p-6 border border-green-200">
              {activeTab === 'overview' && (
                <motion.div
                  className="space-y-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="text-center mb-6">
                    <h3 className="typography-h2 font-semibold text-black mb-4 font-['Open_Sans']">
                      Platform Overview
                    </h3>
                    <p className="typography-body text-black max-w-3xl mx-auto font-normal font-['Open_Sans']">
                      A comprehensive digital solution that transforms manual ESG reporting into an efficient, accurate, and compliant process.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {businessBenefits.map((benefit, index) => {
                      const IconComponent = benefit.icon;
                      return (
                        <MobileCard key={index} className="relative overflow-hidden">
                          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-600" />
                          <div className="flex items-start space-x-4">
                            <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-3 rounded-lg flex-shrink-0 mt-0.5">
                              <IconComponent className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="typography-h4 font-semibold text-black mb-3 font-['Open_Sans']">
                                {benefit.title}
                              </h4>
                              <p className="typography-body text-black font-normal font-['Open_Sans']">
                                {benefit.description}
                              </p>
                            </div>
                          </div>
                        </MobileCard>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {activeTab === 'benefits' && (
  <motion.div
    className="space-y-6"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6 }}
  >
    <div className="text-center mb-6">
      <h3 className="typography-h2 font-semibold text-black mb-4 font-['Open_Sans']">
        Key Benefits
      </h3>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {keyBenefits.map((benefit, index) => {
        const IconComponent = benefit.icon;
        return (
          <MobileCard
            key={index}
            // className="cursor-pointer"
            // onClick={() => setSelectedModule(selectedModule === index ? null : index)}
          >
            <div className="flex items-start space-x-3 mb-3">
              <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-1.5 sm:p-2 rounded-lg flex-shrink-0">
                <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="typography-h4 font-semibold text-black font-['Open_Sans']">
                  {benefit.title}
                </h4>
              </div>
            </div>
            <p className="text-black font-normal typography-body mb-4 font-['Open_Sans']">
              {benefit.description}
            </p>
            {selectedModule === index && (
              <motion.div
                className="space-y-2 pt-3 border-t border-green-200"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                {benefit.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center space-x-2">
                    <CheckCircle className="w-3 h-3 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700 typography-small font-['Open_Sans']">{feature}</span>
                  </div>
                ))}
              </motion.div>
            )}
          </MobileCard>
                      );
                    })}
                  </div>
                </motion.div>
              )}

                {activeTab === 'features' && (
               <motion.div
                  className="space-y-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="text-center mb-6">
                    <h3 className="typography-h2 font-semibold text-black mb-4 font-['Open_Sans']">
                      Core Platform Features
                    </h3>
                    <p className="typography-body text-black max-w-3xl mx-auto font-normal font-['Open_Sans']">
                      Comprehensive ESG reporting capabilities designed for modern enterprises.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {coreFeatures.map((feature, index) => (
                      <MobileCard key={index}>
                        <div className="flex items-start space-x-4">
                          <div className="bg-green-600 p-2 rounded-lg flex-shrink-0 mt-0.5">
                            <CheckCircle className="w-4 h-4 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="typography-body text-gray-700 font-['Open_Sans']">
                              {feature}
                            </p>
                          </div>
                        </div>
                      </MobileCard>
                    ))}
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="px-4 py-8 sm:px-6 sm:py-12 bg-gradient-to-r from-green-50 to-emerald-50"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <div className="max-w-4xl mx-auto text-center">
            <MobileCard>
              <h2 className="typography-h1 font-bold text-black mb-6 font-['Open_Sans']">
                Ready to Transform Your ESG Reporting?
              </h2>
              <p className="typography-body text-black font-normal mb-8 font-['Open_Sans']">
                Connect with our ESG specialists to design a comprehensive digital platform tailored to your specific compliance requirements.
                Experience the transition from manual to digital ESG reporting with industry-leading accuracy and audit-friendly processes.
              </p>
              <motion.button
                onClick={() => navigate('/contact/sales')}
                className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold typography-body rounded-full shadow-lg w-full sm:w-auto justify-center font-['Open_Sans']"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Users className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Contact ESG Specialists
              </motion.button>
            </MobileCard>
          </div>
        </motion.div>
      </div>
    </PageLayout>
  );
};

export default EnterpriseESGReporting;